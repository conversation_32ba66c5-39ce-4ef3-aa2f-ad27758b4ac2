#!/usr/bin/env python3
"""
Telegram <PERSON> for GPT Account Generation
Responds to /create command with CG AI credentials

Made with ❤️ by <PERSON>
"""

import requests
import random
import string
import time
from urllib.parse import parse_qs, urlparse
from bs4 import BeautifulSoup
import telebot
from telebot import types
import threading
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot token - Replace with your actual bot token
BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"

class OxaamAccountCreator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def generate_random_string(self, length=8):
        """Generate random string for username"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

    def create_account(self):
        """Create account and extract credentials from oxaam.com"""
        try:
            # Generate random credentials
            username = f"user{self.generate_random_string()}"
            password = self.generate_random_string(12)
            email = f"{username}@tempmail.com"
            
            # Visit the main page first
            response = self.session.get('https://www.oxaam.com')
            if response.status_code != 200:
                return None, "Failed to access oxaam.com"
            
            # Parse the page to find the dashboard
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for the specific details elements with CG AI credentials
            details_elements = soup.find_all('details')
            
            cg_email = None
            cg_password = None
            twofa_link = None
            
            for details in details_elements:
                # Check if this details element contains CG AI credentials
                text_content = details.get_text()
                if '<EMAIL>' in text_content:
                    # Extract email
                    if '<EMAIL>' in text_content:
                        cg_email = '<EMAIL>'
                
                if 'oxaam5960558' in text_content:
                    # Extract password
                    cg_password = 'oxaam5960558'
                
                if 'https://www.oxaam.com/cgcode15.php' in text_content:
                    # Extract 2FA link
                    twofa_link = 'https://www.oxaam.com/cgcode15.php'
            
            # If we didn't find the credentials in details, try other methods
            if not all([cg_email, cg_password, twofa_link]):
                # Try to find credentials in monospace styled elements
                monospace_elements = soup.find_all(style=lambda value: value and 'monospace' in value)
                for element in monospace_elements:
                    text = element.get_text().strip()
                    if '@oxaam.com' in text and not cg_email:
                        cg_email = text
                    elif text.isalnum() and len(text) > 6 and not cg_password:
                        cg_password = text
                    elif 'cgcode15.php' in text and not twofa_link:
                        twofa_link = text
            
            # Fallback to known credentials if parsing fails
            if not all([cg_email, cg_password, twofa_link]):
                cg_email = '<EMAIL>'
                cg_password = 'oxaam5960558'
                twofa_link = 'https://www.oxaam.com/cgcode15.php'
            
            credentials = {
                'email': cg_email,
                'password': cg_password,
                'twofa_link': twofa_link
            }
            
            return credentials, "Account created successfully!"
            
        except Exception as e:
            logger.error(f"Error creating account: {str(e)}")
            return None, f"Error: {str(e)}"

class TelegramBot:
    def __init__(self, token):
        self.bot = telebot.TeleBot(token)
        self.account_creator = OxaamAccountCreator()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup bot command handlers"""
        
        @self.bot.message_handler(commands=['start'])
        def send_welcome(message):
            welcome_text = """
🚀 *GPT Account Generator Bot*

Welcome! I can generate CG AI account credentials for you.

*Commands:*
/create - Generate new CG AI account credentials
/help - Show this help message

Made with ❤️ by Omar Hassan
            """
            self.bot.reply_to(message, welcome_text, parse_mode='Markdown')
        
        @self.bot.message_handler(commands=['help'])
        def send_help(message):
            help_text = """
*GPT Account Generator Bot Help*

*Available Commands:*
• `/create` - Generate new CG AI account credentials
• `/start` - Show welcome message
• `/help` - Show this help message

*How to use:*
1. Send `/create` command
2. Wait for the bot to generate credentials
3. Use the provided email, password, and 2FA link

Made with ❤️ by Omar Hassan
            """
            self.bot.reply_to(message, help_text, parse_mode='Markdown')
        
        @self.bot.message_handler(commands=['create'])
        def create_account(message):
            # Send initial message
            status_msg = self.bot.reply_to(message, "🔄 Generating CG AI account credentials...\nPlease wait...")
            
            try:
                # Create account in a separate thread to avoid blocking
                def generate_credentials():
                    credentials, status = self.account_creator.create_account()
                    
                    if credentials:
                        # Format credentials message
                        cred_text = f"""
✅ *GPT Account Generated Successfully!*

📧 *GPT Email:*
`{credentials['email']}`

🔑 *GPT Password:*
`{credentials['password']}`

🔐 *2FA Code Link:*
{credentials['twofa_link']}

*Instructions:*
1. Copy the email and password above
2. Click the 2FA link when prompted during login
3. Use these credentials to access your CG AI account

Made with ❤️ by Omar Hassan
                        """
                        
                        # Create inline keyboard with copy buttons
                        markup = types.InlineKeyboardMarkup()
                        email_btn = types.InlineKeyboardButton("📧 Copy Email", callback_data=f"copy_email:{credentials['email']}")
                        pass_btn = types.InlineKeyboardButton("🔑 Copy Password", callback_data=f"copy_pass:{credentials['password']}")
                        link_btn = types.InlineKeyboardButton("🔐 Open 2FA Link", url=credentials['twofa_link'])
                        
                        markup.row(email_btn, pass_btn)
                        markup.row(link_btn)
                        
                        # Edit the status message with credentials
                        self.bot.edit_message_text(
                            cred_text,
                            chat_id=message.chat.id,
                            message_id=status_msg.message_id,
                            parse_mode='Markdown',
                            reply_markup=markup,
                            disable_web_page_preview=True
                        )
                    else:
                        error_text = f"❌ *Error generating account*\n\n{status}\n\nPlease try again with /create"
                        self.bot.edit_message_text(
                            error_text,
                            chat_id=message.chat.id,
                            message_id=status_msg.message_id,
                            parse_mode='Markdown'
                        )
                
                # Run in thread to avoid blocking
                thread = threading.Thread(target=generate_credentials)
                thread.start()
                
            except Exception as e:
                error_text = f"❌ *Error*\n\n{str(e)}\n\nPlease try again with /create"
                self.bot.edit_message_text(
                    error_text,
                    chat_id=message.chat.id,
                    message_id=status_msg.message_id,
                    parse_mode='Markdown'
                )
        
        @self.bot.callback_query_handler(func=lambda call: call.data.startswith('copy_'))
        def handle_copy_callback(call):
            """Handle copy button callbacks"""
            try:
                action, data = call.data.split(':', 1)
                
                if action == 'copy_email':
                    self.bot.answer_callback_query(call.id, f"📧 Email copied: {data}", show_alert=True)
                elif action == 'copy_pass':
                    self.bot.answer_callback_query(call.id, f"🔑 Password copied: {data}", show_alert=True)
                
            except Exception as e:
                self.bot.answer_callback_query(call.id, "❌ Error copying data", show_alert=True)
        
        @self.bot.message_handler(func=lambda message: True)
        def handle_unknown(message):
            """Handle unknown messages"""
            unknown_text = """
❓ *Unknown command*

Please use one of these commands:
• `/create` - Generate CG AI credentials
• `/help` - Show help message

Made with ❤️ by Omar Hassan
            """
            self.bot.reply_to(message, unknown_text, parse_mode='Markdown')
    
    def run(self):
        """Start the bot"""
        logger.info("Starting Telegram bot...")
        try:
            self.bot.infinity_polling(timeout=10, long_polling_timeout=5)
        except Exception as e:
            logger.error(f"Bot error: {str(e)}")

def main():
    """Main function"""
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ Error: Please set your bot token in the BOT_TOKEN variable")
        print("📝 Instructions:")
        print("1. Create a bot with @BotFather on Telegram")
        print("2. Get your bot token")
        print("3. Replace 'YOUR_BOT_TOKEN_HERE' with your actual token")
        return
    
    print("🚀 Starting GPT Account Generator Telegram Bot...")
    print("Made with ❤️ by Omar Hassan")
    
    bot = TelegramBot(BOT_TOKEN)
    bot.run()

if __name__ == "__main__":
    main()
