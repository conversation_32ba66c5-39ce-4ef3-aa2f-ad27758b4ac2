#!/usr/bin/env python3
"""
Oxaam Account Creator - GUI Version
Simple GUI with copy buttons for CG AI credentials
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import requests
import random
import string
import time
from bs4 import BeautifulSoup
from faker import Faker
import re
from urllib.parse import urljoin
import pyperclip

class OxaamGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Oxaam Account Creator")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Initialize session
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://www.oxaam.com"
        
        # Browser headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 Oxaam Account Creator", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Create Account Button
        self.create_btn = ttk.Button(main_frame, text="Create New Account", 
                                    command=self.create_account_thread,
                                    style='Accent.TButton')
        self.create_btn.grid(row=1, column=0, columnspan=3, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Click 'Create New Account' to start", 
                                     font=('Arial', 10))
        self.status_label.grid(row=3, column=0, columnspan=3, pady=(0, 20))
        
        # Credentials frame
        cred_frame = ttk.LabelFrame(main_frame, text="CG AI Credentials", padding="15")
        cred_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Email
        ttk.Label(cred_frame, text="📧 Email:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(cred_frame, textvariable=self.email_var, width=35, state='readonly')
        self.email_entry.grid(row=0, column=1, padx=(10, 5), pady=5)
        self.email_copy_btn = ttk.Button(cred_frame, text="📋", width=3, 
                                        command=lambda: self.copy_to_clipboard(self.email_var.get()))
        self.email_copy_btn.grid(row=0, column=2, padx=(5, 0), pady=5)
        
        # Password
        ttk.Label(cred_frame, text="🔑 Password:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(cred_frame, textvariable=self.password_var, width=35, state='readonly')
        self.password_entry.grid(row=1, column=1, padx=(10, 5), pady=5)
        self.password_copy_btn = ttk.Button(cred_frame, text="📋", width=3,
                                           command=lambda: self.copy_to_clipboard(self.password_var.get()))
        self.password_copy_btn.grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # 2FA Link
        ttk.Label(cred_frame, text="🔐 2FA Link:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.link_var = tk.StringVar()
        self.link_entry = ttk.Entry(cred_frame, textvariable=self.link_var, width=35, state='readonly')
        self.link_entry.grid(row=2, column=1, padx=(10, 5), pady=5)
        self.link_copy_btn = ttk.Button(cred_frame, text="📋", width=3,
                                       command=lambda: self.copy_to_clipboard(self.link_var.get()))
        self.link_copy_btn.grid(row=2, column=2, padx=(5, 0), pady=5)
        
        # Instructions
        instructions = """💡 How to use:
1. Go to https://chatgpt.com
2. Login with the email and password above
3. If asked for verification, visit the 2FA link
4. Select 'oxaam.com' workspace for premium features"""
        
        instructions_label = ttk.Label(main_frame, text=instructions, 
                                      font=('Arial', 9), foreground='gray')
        instructions_label.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        # Initially disable copy buttons
        self.email_copy_btn.configure(state='disabled')
        self.password_copy_btn.configure(state='disabled')
        self.link_copy_btn.configure(state='disabled')
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        cred_frame.columnconfigure(1, weight=1)
        
    def copy_to_clipboard(self, text):
        """Copy text to clipboard"""
        try:
            pyperclip.copy(text)
            messagebox.showinfo("Copied", f"Copied to clipboard!")
        except Exception as e:
            # Fallback to tkinter clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", f"Copied to clipboard!")
    
    def generate_account_data(self):
        """Generate dummy account data"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        
        return {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",
            'password': ''.join(random.choice(string.ascii_letters + string.digits + "!@#$%") for _ in range(10)),
            'country': 'France'
        }
    
    def decode_cf_email(self, encoded):
        """Decode CloudFlare email protection"""
        try:
            if not encoded:
                return None
            key = int(encoded[:2], 16)
            decoded = ''
            for i in range(2, len(encoded), 2):
                decoded += chr(int(encoded[i:i+2], 16) ^ key)
            return decoded
        except:
            return None
    
    def create_account(self):
        """Create account and extract credentials"""
        try:
            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Generating account data..."))
            
            # Generate data
            account_data = self.generate_account_data()
            
            self.root.after(0, lambda: self.status_label.config(text=f"Creating account: {account_data['email']}"))
            
            # Submit registration
            response = self.session.post(self.base_url, data=account_data, allow_redirects=True)
            
            if response.status_code == 200 and "CG-AI" in response.text:
                self.root.after(0, lambda: self.status_label.config(text="Account created! Extracting credentials..."))
                
                # Extract credentials
                credentials = self.extract_credentials(response)
                
                if credentials and credentials['email'] and credentials['password']:
                    # Update UI with credentials
                    self.root.after(0, lambda: self.update_credentials(credentials))
                    self.root.after(0, lambda: self.status_label.config(text="✅ CG AI credentials extracted successfully!"))
                else:
                    self.root.after(0, lambda: self.status_label.config(text="❌ Could not extract CG AI credentials"))
            else:
                self.root.after(0, lambda: self.status_label.config(text="❌ Account creation failed"))
                
        except Exception as e:
            self.root.after(0, lambda: self.status_label.config(text=f"❌ Error: {str(e)}"))
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self.finish_creation)
    
    def extract_credentials(self, response):
        """Extract CG AI credentials from dashboard"""
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find CG-AI section
            cg_ai_section = soup.find('details')
            if not cg_ai_section:
                return None
                
            summary = cg_ai_section.find('summary')
            if not summary or 'CG-AI' not in summary.get_text():
                return None
            
            # Extract email
            email_element = cg_ai_section.find('a', class_='__cf_email__')
            email = None
            if email_element:
                email_encoded = email_element.get('data-cfemail', '')
                email = self.decode_cf_email(email_encoded)
            
            # Extract password
            password = None
            cred_div = cg_ai_section.find('div', style=lambda x: x and 'monospace' in x)
            if cred_div:
                text_content = cred_div.get_text()
                password_match = re.search(r'Password\s*➜\s*([a-zA-Z0-9]+)', text_content)
                if password_match:
                    password = password_match.group(1)
            
            # Extract verification link
            verification_link = None
            verification_element = cg_ai_section.find('a', href=re.compile(r'cgcode'))
            if verification_element:
                verification_link = verification_element.get('href')
                if not verification_link.startswith('http'):
                    verification_link = urljoin(self.base_url, verification_link)
            
            return {
                'email': email,
                'password': password,
                'verification_link': verification_link
            }
            
        except Exception as e:
            return None
    
    def update_credentials(self, credentials):
        """Update UI with extracted credentials"""
        self.email_var.set(credentials['email'] or 'Not found')
        self.password_var.set(credentials['password'] or 'Not found')
        self.link_var.set(credentials['verification_link'] or 'Not found')
        
        # Enable copy buttons
        self.email_copy_btn.configure(state='normal')
        self.password_copy_btn.configure(state='normal')
        self.link_copy_btn.configure(state='normal')
    
    def create_account_thread(self):
        """Start account creation in a separate thread"""
        # Disable button and start progress
        self.create_btn.configure(state='disabled')
        self.progress.start()
        
        # Clear previous credentials
        self.email_var.set('')
        self.password_var.set('')
        self.link_var.set('')
        self.email_copy_btn.configure(state='disabled')
        self.password_copy_btn.configure(state='disabled')
        self.link_copy_btn.configure(state='disabled')
        
        # Start creation in background thread
        thread = threading.Thread(target=self.create_account, daemon=True)
        thread.start()
    
    def finish_creation(self):
        """Re-enable UI after account creation"""
        self.create_btn.configure(state='normal')
        self.progress.stop()

def main():
    # Check if pyperclip is available, if not use tkinter clipboard
    try:
        import pyperclip
    except ImportError:
        print("Note: pyperclip not installed. Using basic clipboard functionality.")
        print("For better clipboard support, install: pip install pyperclip")
    
    root = tk.Tk()
    app = OxaamGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
