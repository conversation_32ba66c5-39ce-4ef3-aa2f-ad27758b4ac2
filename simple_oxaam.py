#!/usr/bin/env python3
"""
Simple Oxaam Account Creator - Minimal version
Creates account and shows only CG AI credentials
"""

import requests
import random
import string
import time
from bs4 import BeautifulSoup
from faker import Faker
import re
from urllib.parse import urljoin

class SimpleOxaamCreator:
    def __init__(self):
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://www.oxaam.com"
        
        # Browser headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
    def generate_account_data(self):
        """Generate dummy account data"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        
        return {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",
            'password': ''.join(random.choice(string.ascii_letters + string.digits + "!@#$%") for _ in range(10)),
            'country': 'France'
        }
    
    def create_account(self):
        """Create account and return dashboard response"""
        try:
            # Generate data
            account_data = self.generate_account_data()
            print(f"Creating account for: {account_data['email']}")
            
            # Submit registration
            response = self.session.post(self.base_url, data=account_data, allow_redirects=True)
            
            if response.status_code == 200 and "CG-AI" in response.text:
                print("✅ Account created successfully!")
                return response
            else:
                print("❌ Account creation failed")
                return None
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
    
    def decode_cf_email(self, encoded):
        """Decode CloudFlare email protection"""
        try:
            if not encoded:
                return None
            key = int(encoded[:2], 16)
            decoded = ''
            for i in range(2, len(encoded), 2):
                decoded += chr(int(encoded[i:i+2], 16) ^ key)
            return decoded
        except:
            return None
    
    def extract_credentials(self, response):
        """Extract CG AI credentials from dashboard"""
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find CG-AI section
            cg_ai_section = soup.find('details')
            if not cg_ai_section:
                return None
                
            summary = cg_ai_section.find('summary')
            if not summary or 'CG-AI' not in summary.get_text():
                return None
            
            # Extract email
            email_element = cg_ai_section.find('a', class_='__cf_email__')
            email = None
            if email_element:
                email_encoded = email_element.get('data-cfemail', '')
                email = self.decode_cf_email(email_encoded)
            
            # Extract password
            password = None
            cred_div = cg_ai_section.find('div', style=lambda x: x and 'monospace' in x)
            if cred_div:
                text_content = cred_div.get_text()
                password_match = re.search(r'Password\s*➜\s*([a-zA-Z0-9]+)', text_content)
                if password_match:
                    password = password_match.group(1)
            
            # Extract verification link
            verification_link = None
            verification_element = cg_ai_section.find('a', href=re.compile(r'cgcode'))
            if verification_element:
                verification_link = verification_element.get('href')
                if not verification_link.startswith('http'):
                    verification_link = urljoin(self.base_url, verification_link)
            
            return {
                'email': email,
                'password': password,
                'verification_link': verification_link
            }
            
        except Exception as e:
            print(f"❌ Error extracting credentials: {e}")
            return None
    
    def run(self):
        """Main function"""
        print("🚀 Creating Oxaam account...")
        
        # Create account
        response = self.create_account()
        if not response:
            return
        
        # Extract credentials
        credentials = self.extract_credentials(response)
        if not credentials:
            print("❌ Could not extract CG AI credentials")
            return
        
        # Display results
        print("\n" + "=" * 40)
        print("🎉 CG AI CREDENTIALS")
        print("=" * 40)
        print(f"📧 Email: {credentials['email']}")
        print(f"🔑 Password: {credentials['password']}")
        print(f"🔐 2FA Code Link: {credentials['verification_link']}")
        print("=" * 40)

def main():
    creator = SimpleOxaamCreator()
    creator.run()

if __name__ == "__main__":
    main()
