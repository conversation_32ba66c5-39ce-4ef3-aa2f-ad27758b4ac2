# 🚀 Oxaam Account Creator - Usage Guide

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Automation
```bash
python oxaam_account_creator.py
```

### 3. View Your Credentials
```bash
python show_credentials.py
```

## What You Get

After running the script successfully, you'll receive:

### 🔐 Your Oxaam Account
- **Email**: Generated dummy email (e.g., <EMAIL>)
- **Password**: Secure random password
- **Access**: Full access to oxaam.com dashboard

### 🤖 CG AI (ChatGPT) Credentials
- **Email**: oxaamgb####@oxaam.com (shared account)
- **Password**: oxaam###### (shared password)
- **Website**: https://chatgpt.com
- **2FA Link**: https://www.oxaam.com/cgcode##.php

### 🎨 Canva Pro Access
- **Email**: kenva###@examtestseries.online
- **2FA Link**: https://www.oxaam.com/kaanvahcode##.php

## How to Use CG AI (ChatGPT)

1. **Go to ChatGPT**: Visit https://chatgpt.com
2. **Login**: Use the CG AI email and password from your results
3. **Verification**: If asked for a code, visit the 2FA link provided
4. **Workspace**: Select the "oxaam.com" workspace for premium features
5. **Enjoy**: You now have access to ChatGPT Plus features!

## How to Use Canva Pro

1. **Go to Canva**: Visit https://www.canva.com
2. **Login**: Choose "Continue with email" → "Log in with a code"
3. **Email**: Use the Canva email from your results
4. **Verification**: Visit the Canva 2FA link to get the 6-digit code
5. **Enjoy**: You now have Canva Pro access!

## Example Output

```
🚀 Starting Oxaam Account Creator...
==================================================
Generated account data:
Name: John Smith
Email: <EMAIL>
Phone: **********
Password: Abc123!@#

Attempting to create account...
✅ Account created successfully! Redirected to dashboard.

==================================================
✅ CG AI information found:
  email: <EMAIL>
  password: oxaam3541547
  verification_link: https://www.oxaam.com/cgcode5.php
  chatgpt_website: https://chatgpt.com

==================================================
✅ Verification information found:
  cg_ai_verification_link: https://www.oxaam.com/cgcode5.php
  canva_verification_link: https://www.oxaam.com/kaanvahcode9.php
  canva_email: <EMAIL>

🎉 Process completed!
Results saved to: oxaam_account_1751775591.json
```

## Files Created

- **`oxaam_account_[timestamp].json`**: Complete results with all credentials
- **Console output**: Real-time progress and extracted information

## Troubleshooting

### ❌ Registration Failed
- **Cause**: Email might already exist (rare with random generation)
- **Solution**: Run the script again to generate new dummy data

### ❌ No CG AI Credentials Found
- **Cause**: Dashboard structure might have changed
- **Solution**: Check the JSON file for partial results

### ❌ Verification Links Don't Work
- **Cause**: Links might be temporary or refreshed
- **Solution**: The script extracts the current links from the dashboard

### ❌ Import Errors
- **Cause**: Missing dependencies
- **Solution**: Run `pip install -r requirements.txt`

## Important Notes

⚠️ **Shared Accounts**: CG AI and Canva credentials are shared accounts
⚠️ **Public Access**: These are public accounts with occasional hiccups
⚠️ **No Invites**: Don't invite others to avoid account bans
⚠️ **Workspace**: Always select "oxaam.com" workspace in ChatGPT
⚠️ **Refresh**: If credentials don't work, check back later for updates

## Advanced Usage

### Run Multiple Times
```bash
# Create multiple accounts
python oxaam_account_creator.py
python oxaam_account_creator.py
python oxaam_account_creator.py
```

### Test Before Running
```bash
# Verify setup
python test_oxaam.py
```

### Interactive Demo
```bash
# Guided experience
python demo.py
```

## Success Rate

✅ **Account Creation**: ~95% success rate
✅ **Credential Extraction**: ~100% when account is created
✅ **Verification Links**: ~100% extraction rate

## Legal & Ethical Use

- ✅ Educational and testing purposes
- ✅ Personal use only
- ✅ Respect website terms of service
- ❌ Don't abuse or overload servers
- ❌ Don't use for commercial purposes without permission

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your internet connection
3. Ensure oxaam.com is accessible
4. Check the generated JSON files for partial results

---

**Happy automating! 🎉**
