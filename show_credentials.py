#!/usr/bin/env python3
"""
Display extracted credentials from the latest Oxaam account creation
"""

import json
import glob
import os
from datetime import datetime

def find_latest_result():
    """Find the most recent result file"""
    pattern = "oxaam_account_*.json"
    files = glob.glob(pattern)
    
    if not files:
        return None
    
    # Sort by modification time, get the latest
    latest_file = max(files, key=os.path.getmtime)
    return latest_file

def display_credentials(filename):
    """Display credentials in a nice format"""
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
        
        print("🎯 OXAAM ACCOUNT CREDENTIALS")
        print("=" * 50)
        
        # Account info
        account = data.get('account_data', {})
        print(f"📧 Your Oxaam Account:")
        print(f"   Email: {account.get('email', 'N/A')}")
        print(f"   Password: {account.get('password', 'N/A')}")
        print(f"   Name: {account.get('name', 'N/A')}")
        
        print("\n" + "=" * 50)
        
        # CG AI credentials
        cg_ai = data.get('cg_ai_credentials', {})
        print(f"🤖 CG AI (ChatGPT) Credentials:")
        print(f"   Email: {cg_ai.get('email', 'N/A')}")
        print(f"   Password: {cg_ai.get('password', 'N/A')}")
        print(f"   Website: {cg_ai.get('chatgpt_website', 'N/A')}")
        print(f"   2FA Code Link: {cg_ai.get('verification_link', 'N/A')}")
        
        print("\n" + "=" * 50)
        
        # Verification info
        verification = data.get('verification_information', {})
        print(f"🔐 Verification Links:")
        print(f"   CG AI Codes: {verification.get('cg_ai_verification_link', 'N/A')}")
        print(f"   Canva Codes: {verification.get('canva_verification_link', 'N/A')}")
        print(f"   Canva Email: {verification.get('canva_email', 'N/A')}")
        
        print("\n" + "=" * 50)
        print(f"📅 Created: {data.get('timestamp', 'N/A')}")
        
        print("\n💡 How to use:")
        print("1. Go to https://chatgpt.com")
        print("2. Login with the CG AI credentials above")
        print("3. If asked for verification, use the 2FA Code Link")
        print("4. Select the 'oxaam.com' workspace for premium features")
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    latest_file = find_latest_result()
    
    if not latest_file:
        print("❌ No result files found!")
        print("Run 'python oxaam_account_creator.py' first to create an account.")
        return
    
    print(f"📁 Reading from: {latest_file}")
    print()
    display_credentials(latest_file)

if __name__ == "__main__":
    main()
