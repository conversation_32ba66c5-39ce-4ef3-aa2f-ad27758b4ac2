#!/usr/bin/env python3
"""
Demo script showing how to use the Oxaam Account Creator
"""

import json
import time
from oxaam_account_creator import OxaamAccountCreator

def main():
    print("🎯 Oxaam Account Creator Demo")
    print("=" * 50)
    
    # Create an instance
    creator = OxaamAccountCreator()
    
    # Show what data will be generated
    print("📋 This script will:")
    print("1. Generate realistic dummy account data")
    print("2. Create an account on oxaam.com")
    print("3. Login to the account")
    print("4. Extract CG AI credentials from dashboard")
    print("5. Find verification code links")
    print("6. Save everything to a JSON file")
    
    print("\n" + "=" * 50)
    
    # Ask for confirmation
    response = input("Do you want to proceed? (y/n): ").lower().strip()
    
    if response != 'y':
        print("❌ Demo cancelled.")
        return
    
    print("\n🚀 Starting automation...")
    
    # Run the automation
    success = creator.run()
    
    if success:
        print("\n✅ Demo completed successfully!")
        print("\nThe script has:")
        print("- Created a new account on oxaam.com")
        print("- Extracted CG AI login credentials")
        print("- Found verification code links")
        print("- Saved all data to a JSON file")
        
        # Show the account details
        print(f"\n📧 Your new account: {creator.account_data['email']}")
        print(f"🔑 Password: {creator.account_data['password']}")
        
        print("\n💡 You can now:")
        print("1. Login to oxaam.com with your new account")
        print("2. Use the CG AI credentials to access ChatGPT")
        print("3. Use the verification links when needed")
        
    else:
        print("\n❌ Demo failed. Check the output above for details.")

if __name__ == "__main__":
    main()
