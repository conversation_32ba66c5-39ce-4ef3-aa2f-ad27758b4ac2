# GPT Account Generator Telegram Bot Setup

## 📋 Prerequisites

1. Python 3.7 or higher
2. Telegram account
3. Internet connection

## 🤖 Creating Your Telegram Bot

1. **Open Telegram** and search for `@BotFather`
2. **Start a chat** with <PERSON><PERSON><PERSON><PERSON> and send `/newbot`
3. **Choose a name** for your bot (e.g., "GPT Account Generator")
4. **Choose a username** for your bot (must end with 'bot', e.g., "gpt_account_gen_bot")
5. **Copy the bot token** that <PERSON><PERSON><PERSON><PERSON> provides (looks like: `*********:ABCdefGHIjklMNOpqrsTUVwxyz`)

## 🛠️ Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure the bot token:**
   - Open `telegram_bot.py`
   - Replace `YOUR_BOT_TOKEN_HERE` with your actual bot token from Bot<PERSON>ather
   
   ```python
   BOT_TOKEN = "*********:ABCdefGHIjklMNOpqrsTUVwxyz"  # Your actual token
   ```

## 🚀 Running the Bot

1. **Start the bot:**
   ```bash
   python telegram_bot.py
   ```

2. **Test the bot:**
   - Open Telegram and search for your bot username
   - Start a chat with your bot
   - Send `/start` to see the welcome message
   - Send `/create` to generate CG AI credentials

## 📱 Bot Commands

- `/start` - Show welcome message
- `/create` - Generate new CG AI account credentials
- `/help` - Show help message

## ✨ Features

- **Instant credential generation** with `/create` command
- **Copy buttons** for easy credential copying
- **Direct 2FA link** button for quick access
- **Dark mode friendly** messages
- **Error handling** with user-friendly messages
- **Threading** to prevent bot blocking during generation

## 🔧 Customization

You can customize the bot by modifying:
- Welcome messages in the `send_welcome()` function
- Credential format in the `create_account()` handler
- Button layouts in the inline keyboard markup
- Error messages and responses

## 🛡️ Security Notes

- Keep your bot token secure and never share it publicly
- The bot generates the same CG AI credentials as specified in your requirements
- All communication is handled through Telegram's secure API

## 📞 Support

If you encounter any issues:
1. Check that your bot token is correct
2. Ensure all dependencies are installed
3. Verify your internet connection
4. Check the console for error messages

Made with ❤️ by Omar Hassan
