#!/usr/bin/env python3
"""
Test script for Oxaam Account Creator
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from oxaam_account_creator import OxaamAccountCreator
    print("✅ Successfully imported OxaamAccountCreator")
    
    # Test data generation
    creator = OxaamAccountCreator()
    dummy_data = creator.generate_dummy_data()
    
    print("\n📋 Generated dummy data:")
    for key, value in dummy_data.items():
        print(f"  {key}: {value}")
    
    print("\n🚀 Ready to run the full automation!")
    print("Run: python oxaam_account_creator.py")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure to install dependencies first:")
    print("pip install -r requirements.txt")
except Exception as e:
    print(f"❌ Error: {e}")
