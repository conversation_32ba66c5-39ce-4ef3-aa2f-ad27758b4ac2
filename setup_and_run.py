#!/usr/bin/env python3
"""
Setup script to install dependencies and run the Oxaam account creator
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def run_account_creator():
    """Run the account creator script"""
    print("Running Oxaam Account Creator...")
    try:
        subprocess.check_call([sys.executable, "oxaam_account_creator.py"])
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to run account creator: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Oxaam Account Creator Setup")
    print("=" * 40)
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        return
    
    # Check if main script exists
    if not os.path.exists("oxaam_account_creator.py"):
        print("❌ oxaam_account_creator.py not found!")
        return
    
    # Install dependencies
    if not install_dependencies():
        return
    
    print("\n" + "=" * 40)
    
    # Run the account creator
    run_account_creator()

if __name__ == "__main__":
    main()
