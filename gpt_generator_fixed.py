#!/usr/bin/env python3
"""
GPT Account Generator - Fixed Dark Mode GUI
Clean, properly styled interface for generating GPT credentials
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import requests
import random
import string
from bs4 import BeautifulSoup
from faker import Faker
import re
import os
from urllib.parse import urljoin

class GPTGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("GPT Account Generator")
        self.root.geometry("600x460")
        self.root.resizable(False, False)

        # Dark mode colors
        self.bg_color = '#2b2b2b'
        self.fg_color = '#ffffff'
        self.entry_bg = '#404040'
        self.button_bg = '#505050'
        self.accent_color = '#0078d4'

        # Configure root
        self.root.configure(bg=self.bg_color)

        # Set program icon for both title bar and taskbar
        icon_files = ['logo.ico', 'icon.ico']
        for icon_file in icon_files:
            if os.path.exists(icon_file):
                try:
                    self.root.iconbitmap(icon_file)
                    # For Windows taskbar icon
                    import sys
                    if sys.platform.startswith('win'):
                        try:
                            import ctypes
                            # Set the app ID to make Windows treat it as a separate app
                            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('GPTAccountGenerator.1.0')
                            # Also try to set the window icon in the taskbar
                            self.root.after(100, lambda: self.root.iconbitmap(icon_file))
                        except:
                            pass
                    print(f"✅ Icon loaded: {icon_file}")
                    break
                except Exception as e:
                    print(f"⚠️ Could not load {icon_file}: {e}")
                    continue
        else:
            print("⚠️ No icon file found (logo.ico or icon.ico)")
            print("The program will run without an icon.")

        # Center window
        self.center_window()

        # Initialize session
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://www.oxaam.com"

        # Browser headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })

        self.setup_ui()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 600
        height = 400
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_ui(self):
        """Setup the user interface"""
        # Main container with dark background
        main_frame = tk.Frame(self.root, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(main_frame, text="🚀 GPT Account Generator",
                              bg=self.bg_color, fg=self.fg_color,
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Create button
        self.create_btn = tk.Button(main_frame, text="Create New Account",
                                   command=self.create_account_thread,
                                   bg=self.accent_color, fg=self.fg_color,
                                   font=('Arial', 11, 'bold'),
                                   relief='flat', padx=20, pady=8,
                                   activebackground='#106ebe', activeforeground=self.fg_color)
        self.create_btn.pack(pady=(0, 15))

        # Progress bar frame
        progress_frame = tk.Frame(main_frame, bg=self.bg_color)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate', length=400)
        self.progress.pack()

        # Status label
        self.status_label = tk.Label(main_frame, text="Click 'Create New Account' to start",
                                    bg=self.bg_color, fg='#cccccc',
                                    font=('Arial', 10))
        self.status_label.pack(pady=(5, 20))

        # Credentials frame
        cred_frame = tk.LabelFrame(main_frame, text="GPT Credentials",
                                  bg=self.bg_color, fg=self.fg_color,
                                  font=('Arial', 11, 'bold'),
                                  relief='solid', bd=1)
        cred_frame.pack(fill=tk.X, pady=(0, 10), padx=10)

        # Email row
        self.create_credential_row(cred_frame, "📧 GPT Email:", "email")

        # Password row
        self.create_credential_row(cred_frame, "🔑 GPT Password:", "password")

        # 2FA Link row
        self.create_credential_row(cred_frame, "🔐 2FA Code Link:", "link")

        # Initially disable copy buttons
        self.disable_copy_buttons()

        # Footer - create separate frame at the bottom of the window
        footer_frame = tk.Frame(self.root, bg=self.bg_color, height=40)
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        footer_frame.pack_propagate(False)  # Maintain fixed height

        footer_label = tk.Label(footer_frame, text="Made with ❤️ by Omar Hassan",
                               bg=self.bg_color, fg='#888888',
                               font=('Arial', 9))
        footer_label.pack(expand=True, pady=(0, 5))  # Center the text vertically with bottom padding

    def create_credential_row(self, parent, label_text, field_name):
        """Create a credential row with label, entry, and copy button"""
        row_frame = tk.Frame(parent, bg=self.bg_color)
        row_frame.pack(fill=tk.X, pady=8, padx=15)

        # Label
        label = tk.Label(row_frame, text=label_text,
                        bg=self.bg_color, fg=self.fg_color,
                        font=('Arial', 10, 'bold'), width=15, anchor='w')
        label.pack(side=tk.LEFT)

        # Entry
        var = tk.StringVar()
        entry = tk.Entry(row_frame, textvariable=var,
                        bg=self.entry_bg, fg=self.fg_color,
                        font=('Consolas', 9), relief='flat',
                        state='readonly', width=35,
                        readonlybackground=self.entry_bg)
        entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        # Copy button
        copy_btn = tk.Button(row_frame, text="Copy",
                            bg=self.button_bg, fg=self.fg_color,
                            font=('Arial', 9), relief='flat', width=8,
                            activebackground='#606060', activeforeground=self.fg_color,
                            command=lambda: self.copy_to_clipboard(var.get(), label_text.split()[-1][:-1]))
        copy_btn.pack(side=tk.RIGHT)

        # Store references
        setattr(self, f"{field_name}_var", var)
        setattr(self, f"{field_name}_entry", entry)
        setattr(self, f"{field_name}_copy_btn", copy_btn)

    def copy_to_clipboard(self, text, field_name):
        """Copy text to clipboard"""
        if not text or text == "":
            messagebox.showwarning("Nothing to Copy", f"No {field_name.lower()} to copy!")
            return

        try:
            # Try pyperclip first
            import pyperclip
            pyperclip.copy(text)
            messagebox.showinfo("Copied!", f"{field_name} copied to clipboard!")
        except ImportError:
            # Fallback to tkinter clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.root.update()
            messagebox.showinfo("Copied!", f"{field_name} copied to clipboard!")
        except Exception as e:
            messagebox.showerror("Copy Failed", f"Failed to copy {field_name.lower()}: {str(e)}")

    def generate_account_data(self):
        """Generate dummy account data"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()

        return {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",
            'password': ''.join(random.choice(string.ascii_letters + string.digits + "!@#$%") for _ in range(10)),
            'country': 'France'
        }

    def decode_cf_email(self, encoded):
        """Decode CloudFlare email protection"""
        try:
            if not encoded:
                return None
            key = int(encoded[:2], 16)
            decoded = ''
            for i in range(2, len(encoded), 2):
                decoded += chr(int(encoded[i:i+2], 16) ^ key)
            return decoded
        except:
            return None

    def create_account(self):
        """Create account and extract credentials"""
        try:
            # Update status
            self.root.after(0, lambda: self.update_status("Generating account data..."))

            # Generate data
            account_data = self.generate_account_data()

            self.root.after(0, lambda: self.update_status("Creating account..."))

            # Submit registration
            response = self.session.post(self.base_url, data=account_data, allow_redirects=True)

            if response.status_code == 200 and "CG-AI" in response.text:
                self.root.after(0, lambda: self.update_status("✅ Account created! Extracting credentials..."))

                # Extract credentials
                credentials = self.extract_credentials(response)

                if credentials and credentials['email'] and credentials['password']:
                    # Update UI with credentials
                    self.root.after(0, lambda: self.update_credentials(credentials))
                    self.root.after(0, lambda: self.update_status("🎉 GPT account generated successfully!"))
                else:
                    self.root.after(0, lambda: self.update_status("❌ Could not extract GPT credentials"))
            else:
                self.root.after(0, lambda: self.update_status("❌ Account creation failed - please try again"))

        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"❌ Error: {str(e)}"))
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self.finish_creation)

    def extract_credentials(self, response):
        """Extract CG AI credentials from dashboard"""
        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # Find CG-AI section
            cg_ai_section = soup.find('details')
            if not cg_ai_section:
                return None

            summary = cg_ai_section.find('summary')
            if not summary or 'CG-AI' not in summary.get_text():
                return None

            # Extract email
            email_element = cg_ai_section.find('a', class_='__cf_email__')
            email = None
            if email_element:
                email_encoded = email_element.get('data-cfemail', '')
                email = self.decode_cf_email(email_encoded)

            # Extract password
            password = None
            cred_div = cg_ai_section.find('div', style=lambda x: x and 'monospace' in x)
            if cred_div:
                text_content = cred_div.get_text()
                password_match = re.search(r'Password\s*➜\s*([a-zA-Z0-9]+)', text_content)
                if password_match:
                    password = password_match.group(1)

            # Extract verification link
            verification_link = None
            verification_element = cg_ai_section.find('a', href=re.compile(r'cgcode'))
            if verification_element:
                verification_link = verification_element.get('href')
                if not verification_link.startswith('http'):
                    verification_link = urljoin(self.base_url, verification_link)

            return {
                'email': email,
                'password': password,
                'verification_link': verification_link
            }

        except Exception as e:
            return None

    def update_status(self, message):
        """Update status label"""
        self.status_label.config(text=message)

    def update_credentials(self, credentials):
        """Update UI with extracted credentials"""
        self.email_var.set(credentials['email'] or 'Not found')
        self.password_var.set(credentials['password'] or 'Not found')
        self.link_var.set(credentials['verification_link'] or 'Not found')

        # Enable copy buttons
        self.enable_copy_buttons()

    def enable_copy_buttons(self):
        """Enable all copy buttons"""
        self.email_copy_btn.configure(state='normal')
        self.password_copy_btn.configure(state='normal')
        self.link_copy_btn.configure(state='normal')

    def disable_copy_buttons(self):
        """Disable all copy buttons"""
        self.email_copy_btn.configure(state='disabled')
        self.password_copy_btn.configure(state='disabled')
        self.link_copy_btn.configure(state='disabled')

    def create_account_thread(self):
        """Start account creation in a separate thread"""
        # Disable button and start progress
        self.create_btn.configure(state='disabled', text="Creating...")
        self.progress.start()

        # Clear previous credentials
        self.email_var.set('')
        self.password_var.set('')
        self.link_var.set('')
        self.disable_copy_buttons()

        # Start creation in background thread
        thread = threading.Thread(target=self.create_account, daemon=True)
        thread.start()

    def finish_creation(self):
        """Re-enable UI after account creation"""
        self.create_btn.configure(state='normal', text="Create New Account")
        self.progress.stop()

def main():
    root = tk.Tk()
    app = GPTGenerator(root)

    # Handle window closing
    def on_closing():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
