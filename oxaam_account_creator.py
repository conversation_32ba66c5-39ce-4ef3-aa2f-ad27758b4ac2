#!/usr/bin/env python3
"""
Oxaam Account Creator and Credential Extractor
Automates account creation on oxaam.com and extracts CG AI credentials and 2FA code link
"""

import requests
import random
import string
import time
from bs4 import BeautifulSoup
from faker import Faker
import re
import json
from urllib.parse import urljoin, urlparse

class OxaamAccountCreator:
    def __init__(self):
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://oxaam.com"
        
        # Set up headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.account_data = {}
        
    def generate_dummy_data(self):
        """Generate realistic dummy data for account creation"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()
        
        self.account_data = {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",  # 10-digit Indian number
            'password': self.generate_password(),
            'country': 'France'  # As specified in the hidden field
        }
        
        print(f"Generated account data:")
        print(f"Name: {self.account_data['name']}")
        print(f"Email: {self.account_data['email']}")
        print(f"Phone: {self.account_data['phone']}")
        print(f"Password: {self.account_data['password']}")
        
        return self.account_data
    
    def generate_password(self):
        """Generate a secure password"""
        length = random.randint(8, 12)
        characters = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(characters) for _ in range(length))
        return password
    
    def get_registration_page(self):
        """Get the registration page and extract any necessary tokens"""
        try:
            print("Fetching registration page...")
            response = self.session.get(self.base_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for CSRF tokens or other hidden fields
            csrf_token = None
            csrf_input = soup.find('input', {'name': 'csrf_token'}) or soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')
                print(f"Found CSRF token: {csrf_token}")
            
            return csrf_token, response.text
            
        except requests.RequestException as e:
            print(f"Error fetching registration page: {e}")
            return None, None
    
    def create_account(self):
        """Create account on oxaam.com"""
        try:
            # Generate dummy data
            self.generate_dummy_data()
            
            # Get registration page first
            csrf_token, page_content = self.get_registration_page()
            
            # Prepare registration data
            registration_data = {
                'name': self.account_data['name'],
                'email': self.account_data['email'],
                'phone': self.account_data['phone'],
                'password': self.account_data['password'],
                'country': self.account_data['country']
            }
            
            # Add CSRF token if found
            if csrf_token:
                registration_data['csrf_token'] = csrf_token
            
            print("Attempting to create account...")
            
            # Submit registration form
            response = self.session.post(
                self.base_url,
                data=registration_data,
                allow_redirects=True
            )
            
            print(f"Registration response status: {response.status_code}")
            
            if response.status_code == 200:
                # Check if registration was successful
                if "login" in response.url.lower() or "dashboard" in response.url.lower():
                    print("✅ Account created successfully!")
                    return True
                elif "error" in response.text.lower() or "already exists" in response.text.lower():
                    print("❌ Registration failed - email might already exist")
                    return False
                else:
                    print("✅ Account creation appears successful")
                    return True
            else:
                print(f"❌ Registration failed with status code: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            print(f"Error during registration: {e}")
            return False
    
    def login_to_account(self):
        """Login to the created account"""
        try:
            print("Attempting to login...")
            
            # Get login page first
            login_url = urljoin(self.base_url, "/login.php")
            response = self.session.get(login_url)
            
            # Extract any CSRF tokens from login page
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_token = None
            csrf_input = soup.find('input', {'name': 'csrf_token'}) or soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')
            
            # Prepare login data
            login_data = {
                'email': self.account_data['email'],
                'password': self.account_data['password']
            }
            
            if csrf_token:
                login_data['csrf_token'] = csrf_token
            
            # Submit login form
            response = self.session.post(login_url, data=login_data, allow_redirects=True)
            
            if response.status_code == 200 and ("dashboard" in response.url.lower() or "profile" in response.url.lower()):
                print("✅ Login successful!")
                return True
            else:
                print("❌ Login failed")
                return False
                
        except requests.RequestException as e:
            print(f"Error during login: {e}")
            return False
    
    def extract_cg_ai_credentials(self):
        """Extract CG AI credentials from the dashboard/profile page"""
        try:
            print("Searching for CG AI credentials...")
            
            # Common URLs where credentials might be found
            potential_urls = [
                "/dashboard.php",
                "/profile.php",
                "/account.php",
                "/services.php",
                "/ai-tools.php",
                "/cg-ai.php",
                "/credentials.php"
            ]
            
            cg_ai_info = {}
            
            for url_path in potential_urls:
                try:
                    url = urljoin(self.base_url, url_path)
                    response = self.session.get(url)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # Look for CG AI related content
                        cg_ai_elements = soup.find_all(text=re.compile(r'CG.*AI|AI.*CG', re.IGNORECASE))
                        
                        if cg_ai_elements:
                            print(f"Found CG AI content on {url}")
                            
                            # Look for credentials in various formats
                            page_text = response.text
                            
                            # Search for API keys
                            api_key_patterns = [
                                r'api[_-]?key["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)',
                                r'token["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)',
                                r'key["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})'
                            ]
                            
                            for pattern in api_key_patterns:
                                matches = re.findall(pattern, page_text, re.IGNORECASE)
                                if matches:
                                    cg_ai_info['api_keys'] = matches
                            
                            # Look for username/password combinations
                            username_pattern = r'username["\s]*[:=]["\s]*([a-zA-Z0-9_@.-]+)'
                            password_pattern = r'password["\s]*[:=]["\s]*([a-zA-Z0-9_!@#$%^&*()-]+)'
                            
                            usernames = re.findall(username_pattern, page_text, re.IGNORECASE)
                            passwords = re.findall(password_pattern, page_text, re.IGNORECASE)
                            
                            if usernames:
                                cg_ai_info['usernames'] = usernames
                            if passwords:
                                cg_ai_info['passwords'] = passwords
                            
                            # Look for any links or URLs
                            links = soup.find_all('a', href=True)
                            cg_ai_links = [link.get('href') for link in links if 'cg' in link.get('href', '').lower() or 'ai' in link.get('href', '').lower()]
                            
                            if cg_ai_links:
                                cg_ai_info['related_links'] = cg_ai_links
                            
                except requests.RequestException:
                    continue
            
            return cg_ai_info
            
        except Exception as e:
            print(f"Error extracting CG AI credentials: {e}")
            return {}
    
    def extract_2fa_code_link(self):
        """Extract 2FA code link from account pages"""
        try:
            print("Searching for 2FA code link...")
            
            # URLs where 2FA setup might be found
            potential_urls = [
                "/dashboard.php",
                "/profile.php",
                "/security.php",
                "/2fa.php",
                "/two-factor.php",
                "/account-security.php"
            ]
            
            twofa_info = {}
            
            for url_path in potential_urls:
                try:
                    url = urljoin(self.base_url, url_path)
                    response = self.session.get(url)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # Look for 2FA related content
                        twofa_elements = soup.find_all(text=re.compile(r'2FA|two.factor|authenticator|google.auth', re.IGNORECASE))
                        
                        if twofa_elements:
                            print(f"Found 2FA content on {url}")
                            
                            # Look for QR codes
                            qr_images = soup.find_all('img', src=re.compile(r'qr|2fa|auth', re.IGNORECASE))
                            if qr_images:
                                qr_links = [urljoin(self.base_url, img.get('src')) for img in qr_images]
                                twofa_info['qr_codes'] = qr_links
                            
                            # Look for setup links
                            setup_links = soup.find_all('a', href=True)
                            twofa_links = [
                                urljoin(self.base_url, link.get('href')) 
                                for link in setup_links 
                                if any(keyword in link.get('href', '').lower() for keyword in ['2fa', 'two-factor', 'auth', 'security'])
                            ]
                            
                            if twofa_links:
                                twofa_info['setup_links'] = twofa_links
                            
                            # Look for secret keys
                            page_text = response.text
                            secret_pattern = r'secret["\s]*[:=]["\s]*([A-Z2-7]{16,})'
                            secrets = re.findall(secret_pattern, page_text, re.IGNORECASE)
                            
                            if secrets:
                                twofa_info['secret_keys'] = secrets
                            
                except requests.RequestException:
                    continue
            
            return twofa_info
            
        except Exception as e:
            print(f"Error extracting 2FA information: {e}")
            return {}
    
    def save_results(self, cg_ai_info, twofa_info):
        """Save all extracted information to a file"""
        results = {
            'account_data': self.account_data,
            'cg_ai_credentials': cg_ai_info,
            'twofa_information': twofa_info,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        filename = f"oxaam_account_{int(time.time())}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to {filename}")
        return filename
    
    def run(self):
        """Main execution function"""
        print("🚀 Starting Oxaam Account Creator...")
        print("=" * 50)
        
        # Step 1: Create account
        if not self.create_account():
            print("❌ Failed to create account. Exiting.")
            return False
        
        # Wait a bit before login
        time.sleep(2)
        
        # Step 2: Login to account
        if not self.login_to_account():
            print("❌ Failed to login. Exiting.")
            return False
        
        # Wait a bit before extracting data
        time.sleep(2)
        
        # Step 3: Extract CG AI credentials
        print("\n" + "=" * 50)
        cg_ai_info = self.extract_cg_ai_credentials()
        
        if cg_ai_info:
            print("✅ CG AI information found:")
            for key, value in cg_ai_info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No CG AI credentials found")
        
        # Step 4: Extract 2FA code link
        print("\n" + "=" * 50)
        twofa_info = self.extract_2fa_code_link()
        
        if twofa_info:
            print("✅ 2FA information found:")
            for key, value in twofa_info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No 2FA information found")
        
        # Step 5: Save results
        print("\n" + "=" * 50)
        filename = self.save_results(cg_ai_info, twofa_info)
        
        print("\n🎉 Process completed!")
        print(f"Account created: {self.account_data['email']}")
        print(f"Results saved to: {filename}")
        
        return True

def main():
    """Main function"""
    creator = OxaamAccountCreator()
    creator.run()

if __name__ == "__main__":
    main()
