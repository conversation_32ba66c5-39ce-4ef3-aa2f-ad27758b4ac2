#!/usr/bin/env python3
"""
Oxaam Account Creator and Credential Extractor
Automates account creation on oxaam.com and extracts CG AI credentials and 2FA code link
"""

import requests
import random
import string
import time
from bs4 import BeautifulSoup
from faker import Faker
import re
import json
from urllib.parse import urljoin, urlparse

class OxaamAccountCreator:
    def __init__(self):
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://www.oxaam.com"

        # Set up headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        self.account_data = {}

    def generate_dummy_data(self):
        """Generate realistic dummy data for account creation"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()

        self.account_data = {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",  # 10-digit Indian number
            'password': self.generate_password(),
            'country': 'France'  # As specified in the hidden field
        }

        print(f"Generated account data:")
        print(f"Name: {self.account_data['name']}")
        print(f"Email: {self.account_data['email']}")
        print(f"Phone: {self.account_data['phone']}")
        print(f"Password: {self.account_data['password']}")

        return self.account_data

    def generate_password(self):
        """Generate a secure password"""
        length = random.randint(8, 12)
        characters = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(characters) for _ in range(length))
        return password

    def get_registration_page(self):
        """Get the registration page and extract any necessary tokens"""
        try:
            print("Fetching registration page...")
            response = self.session.get(self.base_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Look for CSRF tokens or other hidden fields
            csrf_token = None
            csrf_input = soup.find('input', {'name': 'csrf_token'}) or soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')
                print(f"Found CSRF token: {csrf_token}")

            return csrf_token, response.text

        except requests.RequestException as e:
            print(f"Error fetching registration page: {e}")
            return None, None

    def create_account(self):
        """Create account on oxaam.com and get redirected to dashboard"""
        try:
            # Generate dummy data
            self.generate_dummy_data()

            # Get registration page first
            csrf_token, page_content = self.get_registration_page()

            # Prepare registration data
            registration_data = {
                'name': self.account_data['name'],
                'email': self.account_data['email'],
                'phone': self.account_data['phone'],
                'password': self.account_data['password'],
                'country': self.account_data['country']
            }

            # Add CSRF token if found
            if csrf_token:
                registration_data['csrf_token'] = csrf_token

            print("Attempting to create account...")

            # Submit registration form
            response = self.session.post(
                self.base_url,
                data=registration_data,
                allow_redirects=True
            )

            print(f"Registration response status: {response.status_code}")
            print(f"Final URL after registration: {response.url}")

            # Debug: Check what's in the response
            if "CG-AI" in response.text:
                print("✅ Found CG-AI content in response!")
            if "Welcome" in response.text:
                print("✅ Found Welcome message in response!")
            if "dashboard" in response.text.lower():
                print("✅ Found dashboard content in response!")

            if response.status_code == 200:
                # Check if we're redirected to dashboard (successful registration)
                if "dashboard" in response.url.lower():
                    print("✅ Account created successfully! Redirected to dashboard.")
                    # Store the dashboard response for immediate credential extraction
                    self.dashboard_response = response
                    return True
                elif "Welcome" in response.text and "CG-AI" in response.text:
                    print("✅ Account created successfully! Dashboard loaded.")
                    # Store the dashboard response for immediate credential extraction
                    self.dashboard_response = response
                    return True
                elif "CG-AI" in response.text:
                    print("✅ Account created successfully! Found CG-AI content.")
                    # Store the dashboard response for immediate credential extraction
                    self.dashboard_response = response
                    return True
                elif "error" in response.text.lower() or "already exists" in response.text.lower():
                    print("❌ Registration failed - email might already exist")
                    # Try to extract error message
                    soup = BeautifulSoup(response.text, 'html.parser')
                    error_elements = soup.find_all(text=re.compile(r'error|already|exists', re.IGNORECASE))
                    if error_elements:
                        print(f"Error details: {error_elements[0][:100]}...")
                    return False
                else:
                    print("✅ Account creation appears successful")
                    # Store the response anyway in case it contains the dashboard
                    self.dashboard_response = response
                    return True
            else:
                print(f"❌ Registration failed with status code: {response.status_code}")
                return False

        except requests.RequestException as e:
            print(f"Error during registration: {e}")
            return False

    def access_dashboard(self):
        """Access dashboard (either from registration redirect or direct access)"""
        try:
            # If we already have dashboard response from registration, use it
            if hasattr(self, 'dashboard_response') and self.dashboard_response:
                print("✅ Using dashboard from registration redirect")
                return True

            print("Attempting to access dashboard directly...")

            # Try to access dashboard directly
            dashboard_url = urljoin(self.base_url, "/dashboard.php")
            response = self.session.get(dashboard_url)

            if response.status_code == 200:
                if "Welcome" in response.text or "CG-AI" in response.text:
                    print("✅ Dashboard accessed successfully!")
                    self.dashboard_response = response
                    return True
                else:
                    print("❌ Dashboard access failed - might need login")
                    return self.try_login()
            else:
                print(f"❌ Dashboard access failed with status code: {response.status_code}")
                return self.try_login()

        except requests.RequestException as e:
            print(f"Error accessing dashboard: {e}")
            return False

    def try_login(self):
        """Try to login if dashboard access fails"""
        try:
            print("Attempting to login...")

            # Get login page first
            login_url = urljoin(self.base_url, "/login.php")
            response = self.session.get(login_url)

            # Extract any CSRF tokens from login page
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_token = None
            csrf_input = soup.find('input', {'name': 'csrf_token'}) or soup.find('input', {'name': '_token'})
            if csrf_input:
                csrf_token = csrf_input.get('value')

            # Prepare login data
            login_data = {
                'email': self.account_data['email'],
                'password': self.account_data['password']
            }

            if csrf_token:
                login_data['csrf_token'] = csrf_token

            # Submit login form
            response = self.session.post(login_url, data=login_data, allow_redirects=True)

            if response.status_code == 200 and ("dashboard" in response.url.lower() or "Welcome" in response.text):
                print("✅ Login successful!")
                self.dashboard_response = response
                return True
            else:
                print("❌ Login failed")
                return False

        except requests.RequestException as e:
            print(f"Error during login: {e}")
            return False

    def extract_cg_ai_credentials(self):
        """Extract CG AI credentials from the dashboard page"""
        try:
            print("Searching for CG AI credentials...")

            # Use stored dashboard response if available, otherwise fetch it
            if hasattr(self, 'dashboard_response') and self.dashboard_response:
                response = self.dashboard_response
                print("Using stored dashboard response")
            else:
                # Get dashboard page
                dashboard_url = urljoin(self.base_url, "/dashboard.php")
                response = self.session.get(dashboard_url)

                if response.status_code != 200:
                    print(f"Failed to access dashboard: {response.status_code}")
                    return {}

            soup = BeautifulSoup(response.text, 'html.parser')
            cg_ai_info = {}

            # Look for CG-AI section in details element
            cg_ai_section = soup.find('details')
            if cg_ai_section:
                summary = cg_ai_section.find('summary')
                if summary and 'CG-AI' in summary.get_text():
                    print("✅ Found CG-AI section in dashboard")

                    # Extract email and password from the credentials div
                    cred_div = cg_ai_section.find('div', style=lambda x: x and 'monospace' in x)
                    if cred_div:
                        # Extract email
                        email_element = cred_div.find('a', class_='__cf_email__')
                        if email_element:
                            email_encoded = email_element.get('data-cfemail', '')
                            # Decode CloudFlare email protection
                            email = self.decode_cf_email(email_encoded)
                            if email:
                                cg_ai_info['email'] = email

                        # Extract password - look for text after "Password ➜"
                        text_content = cred_div.get_text()
                        password_match = re.search(r'Password\s*➜\s*([a-zA-Z0-9]+)', text_content)
                        if password_match:
                            cg_ai_info['password'] = password_match.group(1)

                    # Extract verification code link
                    verification_link = cg_ai_section.find('a', href=re.compile(r'cgcode'))
                    if verification_link:
                        cg_ai_info['verification_link'] = verification_link.get('href')
                        if not cg_ai_info['verification_link'].startswith('http'):
                            cg_ai_info['verification_link'] = urljoin(self.base_url, cg_ai_info['verification_link'])

                    # Extract ChatGPT website link
                    chatgpt_link = cg_ai_section.find('a', href=re.compile(r'chatgpt\.com'))
                    if chatgpt_link:
                        cg_ai_info['chatgpt_website'] = chatgpt_link.get('href')

            # Also look for any other credential patterns in the page
            page_text = response.text

            # Look for additional email patterns
            if 'email' not in cg_ai_info:
                email_patterns = [
                    r'oxaamgb\d+@oxaam\.com',
                    r'[a-zA-Z0-9._%+-]+@oxaam\.com'
                ]
                for pattern in email_patterns:
                    matches = re.findall(pattern, page_text)
                    if matches:
                        cg_ai_info['email'] = matches[0]
                        break

            # Look for password patterns
            if 'password' not in cg_ai_info:
                password_patterns = [
                    r'oxaam\d+',
                    r'Password\s*➜\s*([a-zA-Z0-9]+)'
                ]
                for pattern in password_patterns:
                    matches = re.findall(pattern, page_text)
                    if matches:
                        cg_ai_info['password'] = matches[0]
                        break

            return cg_ai_info

        except Exception as e:
            print(f"Error extracting CG AI credentials: {e}")
            return {}

    def decode_cf_email(self, encoded):
        """Decode CloudFlare email protection"""
        try:
            if not encoded:
                return None

            # CloudFlare email decoding
            key = int(encoded[:2], 16)
            decoded = ''
            for i in range(2, len(encoded), 2):
                decoded += chr(int(encoded[i:i+2], 16) ^ key)
            return decoded
        except:
            return None

    def extract_2fa_code_link(self):
        """Extract 2FA/verification code links from dashboard"""
        try:
            print("Searching for verification code links...")

            # Use stored dashboard response if available, otherwise fetch it
            if hasattr(self, 'dashboard_response') and self.dashboard_response:
                response = self.dashboard_response
                print("Using stored dashboard response")
            else:
                # Get dashboard page
                dashboard_url = urljoin(self.base_url, "/dashboard.php")
                response = self.session.get(dashboard_url)

                if response.status_code != 200:
                    print(f"Failed to access dashboard: {response.status_code}")
                    return {}

            soup = BeautifulSoup(response.text, 'html.parser')
            verification_info = {}

            # Look for all details sections (CG-AI and Canva)
            details_sections = soup.find_all('details')

            for section in details_sections:
                summary = section.find('summary')
                if not summary:
                    continue

                summary_text = summary.get_text()

                # CG-AI verification link
                if 'CG-AI' in summary_text:
                    print("✅ Found CG-AI verification section")
                    cg_link = section.find('a', href=re.compile(r'cgcode'))
                    if cg_link:
                        link_url = cg_link.get('href')
                        if not link_url.startswith('http'):
                            link_url = urljoin(self.base_url, link_url)
                        verification_info['cg_ai_verification_link'] = link_url
                        print(f"  CG-AI verification link: {link_url}")

                # Canva verification link
                elif 'Kaanvah' in summary_text or 'Canva' in summary_text:
                    print("✅ Found Canva verification section")
                    canva_link = section.find('a', href=re.compile(r'kaanvahcode'))
                    if canva_link:
                        link_url = canva_link.get('href')
                        if not link_url.startswith('http'):
                            link_url = urljoin(self.base_url, link_url)
                        verification_info['canva_verification_link'] = link_url
                        print(f"  Canva verification link: {link_url}")

                    # Also extract Canva email
                    canva_email_element = section.find('a', class_='__cf_email__')
                    if canva_email_element:
                        email_encoded = canva_email_element.get('data-cfemail', '')
                        email = self.decode_cf_email(email_encoded)
                        if email:
                            verification_info['canva_email'] = email
                            print(f"  Canva email: {email}")

            # Look for any other verification code links in the page
            page_text = response.text

            # Search for verification link patterns
            verification_patterns = [
                r'https://www\.oxaam\.com/cgcode\d+\.php',
                r'https://www\.oxaam\.com/kaanvahcode\d+\.php',
                r'/cgcode\d+\.php',
                r'/kaanvahcode\d+\.php'
            ]

            for pattern in verification_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    for match in matches:
                        if not match.startswith('http'):
                            match = urljoin(self.base_url, match)

                        if 'cgcode' in match and 'cg_ai_verification_link' not in verification_info:
                            verification_info['cg_ai_verification_link'] = match
                        elif 'kaanvahcode' in match and 'canva_verification_link' not in verification_info:
                            verification_info['canva_verification_link'] = match

            return verification_info

        except Exception as e:
            print(f"Error extracting verification information: {e}")
            return {}

    def save_results(self, cg_ai_info, verification_info):
        """Save all extracted information to a file"""
        results = {
            'account_data': self.account_data,
            'cg_ai_credentials': cg_ai_info,
            'verification_information': verification_info,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        filename = f"oxaam_account_{int(time.time())}.json"

        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)

        print(f"Results saved to {filename}")
        return filename

    def run(self):
        """Main execution function"""
        print("🚀 Starting Oxaam Account Creator...")
        print("=" * 50)

        # Step 1: Create account
        if not self.create_account():
            print("❌ Failed to create account. Exiting.")
            return False

        # Wait a bit before login
        time.sleep(2)

        # Step 2: Access dashboard (might already be available from registration)
        if not self.access_dashboard():
            print("❌ Failed to access dashboard. Exiting.")
            return False

        # Wait a bit before extracting data
        time.sleep(1)

        # Step 3: Extract CG AI credentials
        print("\n" + "=" * 50)
        cg_ai_info = self.extract_cg_ai_credentials()

        if cg_ai_info:
            print("✅ CG AI information found:")
            for key, value in cg_ai_info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No CG AI credentials found")

        # Step 4: Extract verification code links
        print("\n" + "=" * 50)
        verification_info = self.extract_2fa_code_link()

        if verification_info:
            print("✅ Verification information found:")
            for key, value in verification_info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No verification information found")

        # Step 5: Save results
        print("\n" + "=" * 50)
        filename = self.save_results(cg_ai_info, verification_info)

        print("\n🎉 Process completed!")
        print(f"Account created: {self.account_data['email']}")
        print(f"Results saved to: {filename}")

        return True

def main():
    """Main function"""
    creator = OxaamAccountCreator()
    creator.run()

if __name__ == "__main__":
    main()
