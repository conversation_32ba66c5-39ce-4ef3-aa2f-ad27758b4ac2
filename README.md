# Oxaam Account Creator

This Python script automates the process of creating an account on oxaam.com with dummy details and extracts CG AI credentials and 2FA code links.

## Features

- ✅ Automatically generates realistic dummy account data
- ✅ Creates account on oxaam.com
- ✅ Logs into the created account
- ✅ Searches for and extracts CG AI credentials
- ✅ Finds 2FA setup links and codes
- ✅ Saves all results to a JSON file

## Requirements

- Python 3.7+
- Internet connection
- Required Python packages (see requirements.txt)

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Or use the setup script:**
   ```bash
   python setup_and_run.py
   ```

## Manual Usage

Run the main script:
```bash
python oxaam_account_creator.py
```

## Generated Data

The script generates realistic dummy data including:
- **Name**: Random first and last name
- **Email**: Generated email with random numbers
- **Phone**: 10-digit phone number
- **Password**: Secure random password
- **Country**: Set to "France" (as per the form)

## Output

The script will:
1. Display the generated account details
2. Show progress of account creation and login
3. Search for CG AI credentials across multiple pages
4. Look for 2FA setup information
5. Save all results to a timestamped JSON file

## Output File Structure

```json
{
  "account_data": {
    "name": "Generated Name",
    "email": "<EMAIL>",
    "phone": "**********",
    "password": "GeneratedPassword",
    "country": "France"
  },
  "cg_ai_credentials": {
    "email": "<EMAIL>",
    "password": "oxaam5960558",
    "verification_link": "https://www.oxaam.com/cgcode15.php",
    "chatgpt_website": "https://chatgpt.com"
  },
  "verification_information": {
    "cg_ai_verification_link": "https://www.oxaam.com/cgcode15.php",
    "canva_verification_link": "https://www.oxaam.com/kaanvahcode5.php",
    "canva_email": "<EMAIL>"
  },
  "timestamp": "2025-01-06 12:00:00"
}
```

## How It Works

1. **Data Generation**: Uses the Faker library to generate realistic dummy data
2. **Account Creation**: Submits the registration form with generated data
3. **Login**: Authenticates with the created account
4. **Credential Extraction**: Parses the dashboard to extract CG AI credentials
5. **Verification Links**: Finds verification code links for CG AI and Canva
6. **CloudFlare Decoding**: Automatically decodes CloudFlare-protected email addresses
7. **Result Storage**: Saves all findings to a timestamped JSON file

## What Gets Extracted

The script extracts the following information from the dashboard:

### CG AI Credentials
- **Email**: The login email for ChatGPT (e.g., <EMAIL>)
- **Password**: The login password (e.g., oxaam5960558)
- **Verification Link**: Link to get verification codes (e.g., /cgcode15.php)
- **ChatGPT Website**: Direct link to chatgpt.com

### Verification Information
- **CG AI Verification Link**: Full URL for CG AI verification codes
- **Canva Verification Link**: Full URL for Canva verification codes
- **Canva Email**: Email address for Canva login

### Additional Features
- **CloudFlare Email Decoding**: Automatically decodes protected email addresses
- **Multiple Service Support**: Extracts credentials for both CG AI and Canva Pro
- **Robust Parsing**: Uses multiple methods to find credentials if one fails

## Error Handling

The script includes comprehensive error handling for:
- Network connection issues
- Failed account creation
- Login failures
- Missing pages or content
- Invalid responses

## Security Notes

- The script uses session management to maintain login state
- Includes realistic browser headers to avoid detection
- Handles CSRF tokens if present
- Uses secure password generation
- Automatically decodes CloudFlare email protection
- Respects website structure and doesn't overload servers

## Example Output

When the script runs successfully, you'll see output like this:

```
🚀 Starting Oxaam Account Creator...
==================================================
Generated account data:
Name: John Smith
Email: <EMAIL>
Phone: **********
Password: Abc123!@#
Attempting to create account...
✅ Account created successfully!
Attempting to login...
✅ Login successful!

==================================================
Searching for CG AI credentials...
✅ Found CG-AI section in dashboard
✅ CG AI information found:
  email: <EMAIL>
  password: oxaam5960558
  verification_link: https://www.oxaam.com/cgcode15.php
  chatgpt_website: https://chatgpt.com

==================================================
Searching for verification code links...
✅ Found CG-AI verification section
  CG-AI verification link: https://www.oxaam.com/cgcode15.php
✅ Found Canva verification section
  Canva verification link: https://www.oxaam.com/kaanvahcode5.php
  Canva email: <EMAIL>
✅ Verification information found:
  cg_ai_verification_link: https://www.oxaam.com/cgcode15.php
  canva_verification_link: https://www.oxaam.com/kaanvahcode5.php
  canva_email: <EMAIL>

==================================================
Results saved to oxaam_account_1704537600.json

🎉 Process completed!
Account created: <EMAIL>
Results saved to: oxaam_account_1704537600.json
```

## Troubleshooting

If the script fails:
1. Check your internet connection
2. Verify that oxaam.com is accessible
3. The email might already exist - the script will notify you
4. Check the generated JSON file for partial results
5. Make sure all dependencies are installed correctly

## Quick Test

To test if everything is working:
```bash
python test_oxaam.py
```

This will verify that all dependencies are installed and the script can generate dummy data.

## Legal Disclaimer

This script is for educational and testing purposes only. Ensure you comply with oxaam.com's terms of service and applicable laws when using this tool.
