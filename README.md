# Oxaam Account Creator

This Python script automates the process of creating an account on oxaam.com with dummy details and extracts CG AI credentials and 2FA code links.

## Features

- ✅ Automatically generates realistic dummy account data
- ✅ Creates account on oxaam.com
- ✅ Logs into the created account
- ✅ Searches for and extracts CG AI credentials
- ✅ Finds 2FA setup links and codes
- ✅ Saves all results to a JSON file

## Requirements

- Python 3.7+
- Internet connection
- Required Python packages (see requirements.txt)

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Or use the setup script:**
   ```bash
   python setup_and_run.py
   ```

## Manual Usage

Run the main script:
```bash
python oxaam_account_creator.py
```

## Generated Data

The script generates realistic dummy data including:
- **Name**: Random first and last name
- **Email**: Generated email with random numbers
- **Phone**: 10-digit phone number
- **Password**: Secure random password
- **Country**: Set to "France" (as per the form)

## Output

The script will:
1. Display the generated account details
2. Show progress of account creation and login
3. Search for CG AI credentials across multiple pages
4. Look for 2FA setup information
5. Save all results to a timestamped JSON file

## Output File Structure

```json
{
  "account_data": {
    "name": "Generated Name",
    "email": "<EMAIL>",
    "phone": "**********",
    "password": "GeneratedPassword",
    "country": "France"
  },
  "cg_ai_credentials": {
    "api_keys": ["found_api_keys"],
    "usernames": ["found_usernames"],
    "passwords": ["found_passwords"],
    "related_links": ["related_urls"]
  },
  "twofa_information": {
    "qr_codes": ["qr_code_urls"],
    "setup_links": ["setup_urls"],
    "secret_keys": ["secret_keys"]
  },
  "timestamp": "2025-01-06 12:00:00"
}
```

## How It Works

1. **Data Generation**: Uses the Faker library to generate realistic dummy data
2. **Account Creation**: Submits the registration form with generated data
3. **Login**: Authenticates with the created account
4. **Credential Extraction**: Searches multiple pages for CG AI related information
5. **2FA Detection**: Looks for two-factor authentication setup links and codes
6. **Result Storage**: Saves all findings to a JSON file

## Pages Searched

The script searches these common pages for credentials and 2FA information:
- `/dashboard.php`
- `/profile.php`
- `/account.php`
- `/services.php`
- `/ai-tools.php`
- `/cg-ai.php`
- `/credentials.php`
- `/security.php`
- `/2fa.php`
- `/two-factor.php`

## Error Handling

The script includes comprehensive error handling for:
- Network connection issues
- Failed account creation
- Login failures
- Missing pages or content
- Invalid responses

## Security Notes

- The script uses session management to maintain login state
- Includes realistic browser headers to avoid detection
- Handles CSRF tokens if present
- Uses secure password generation

## Troubleshooting

If the script fails:
1. Check your internet connection
2. Verify that oxaam.com is accessible
3. The email might already exist - the script will notify you
4. Check the generated JSON file for partial results

## Legal Disclaimer

This script is for educational and testing purposes only. Ensure you comply with oxaam.com's terms of service and applicable laws when using this tool.
