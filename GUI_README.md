# 🚀 Oxaam Account Creator - GUI Version

A simple graphical interface for creating Oxaam accounts and extracting CG AI credentials.

## Features

✅ **Clean GUI Interface** - Easy to use graphical interface
✅ **One-Click Account Creation** - Just click "Create New Account"
✅ **Copy Buttons** - Copy email, password, and 2FA link with one click
✅ **Real-time Status** - See progress as account is being created
✅ **No File Saving** - Clean, minimal approach

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the GUI:**
   ```bash
   python oxaam_gui.py
   ```

## How to Use

1. **Launch the GUI** - Run `python oxaam_gui.py`
2. **Click "Create New Account"** - The program starts blank
3. **Wait for completion** - Progress bar shows the process
4. **Copy credentials** - Use the 📋 buttons to copy each field
5. **Use CG AI** - Go to https://chatgpt.com and login

## GUI Layout

```
🚀 Oxaam Account Creator
┌─────────────────────────────────────┐
│        [Create New Account]         │
│                                     │
│ ████████████████████████████████    │ ← Progress Bar
│ Status: Creating account...         │
│                                     │
│ ┌─ CG AI Credentials ─────────────┐ │
│ │ 📧 Email:    [<EMAIL>] 📋│ │
│ │ 🔑 Password: [password123]     📋│ │
│ │ 🔐 2FA Link: [https://...]     📋│ │
│ └─────────────────────────────────┘ │
│                                     │
│ 💡 How to use:                      │
│ 1. Go to https://chatgpt.com        │
│ 2. Login with credentials above     │
│ 3. Use 2FA link if needed          │
│ 4. Select 'oxaam.com' workspace    │
└─────────────────────────────────────┘
```

## What You Get

After clicking "Create New Account", you'll see:

### 📧 CG AI Email
- Format: `oxaamgb####@oxaam.com`
- Click 📋 to copy to clipboard

### 🔑 CG AI Password  
- Format: `oxaam######`
- Click 📋 to copy to clipboard

### 🔐 2FA Code Link
- Format: `https://www.oxaam.com/cgcode##.php`
- Click 📋 to copy to clipboard
- Visit this link when ChatGPT asks for verification

## Process Flow

1. **Click Create** → GUI generates dummy account data
2. **Account Creation** → Submits registration to oxaam.com
3. **Auto Redirect** → Gets redirected to dashboard with credentials
4. **Credential Extraction** → Parses CG AI email, password, and 2FA link
5. **Display Results** → Shows credentials with copy buttons

## Example Usage

1. **Start the GUI**
2. **Click "Create New Account"**
3. **Wait ~10-30 seconds**
4. **See credentials appear:**
   - Email: `<EMAIL>` 📋
   - Password: `oxaam3541547` 📋  
   - 2FA Link: `https://www.oxaam.com/cgcode5.php` 📋
5. **Copy and use for ChatGPT login**

## Advantages of GUI Version

- ✅ **User-friendly** - No command line needed
- ✅ **Copy buttons** - Easy clipboard access
- ✅ **Visual feedback** - Progress bar and status updates
- ✅ **Clean interface** - Professional looking GUI
- ✅ **Reusable** - Create multiple accounts without restarting

## Technical Details

- **Framework**: tkinter (built into Python)
- **Threading**: Background account creation to keep GUI responsive
- **Clipboard**: pyperclip for reliable copy functionality
- **Error Handling**: User-friendly error messages
- **Cross-platform**: Works on Windows, Mac, and Linux

## Troubleshooting

### GUI Won't Start
- Make sure Python has tkinter: `python -m tkinter`
- Install dependencies: `pip install -r requirements.txt`

### Copy Buttons Don't Work
- pyperclip dependency issue
- GUI will fallback to basic tkinter clipboard

### Account Creation Fails
- Check internet connection
- Try again (email might already exist)
- Check status message for details

---

**Enjoy the GUI experience! 🎉**
