#!/usr/bin/env python3
"""
Oxaam Account Creator - GUI Version 2
Simple GUI with copy buttons for CG AI credentials
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import requests
import random
import string
from bs4 import BeautifulSoup
from faker import Faker
import re
from urllib.parse import urljoin

class OxaamGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPT Account Generator")
        self.root.geometry("600x350")
        self.root.resizable(True, False)

        # Center the window
        self.center_window()

        # Initialize session
        self.session = requests.Session()
        self.fake = Faker()
        self.base_url = "https://www.oxaam.com"

        # Browser headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })

        self.setup_ui()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_ui(self):
        """Setup the user interface"""
        # Configure dark mode style
        style = ttk.Style()

        # Set dark theme colors
        bg_color = '#2b2b2b'
        fg_color = '#ffffff'
        select_bg = '#404040'
        select_fg = '#ffffff'

        # Configure root window
        self.root.configure(bg=bg_color)

        # Configure styles for dark mode
        style.configure('Title.TLabel', font=('Arial', 18, 'bold'),
                       background=bg_color, foreground=fg_color)
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'),
                       background=bg_color, foreground=fg_color)
        style.configure('Create.TButton', font=('Arial', 12, 'bold'), padding=10)
        style.configure('TLabel', background=bg_color, foreground=fg_color)
        style.configure('TFrame', background=bg_color)
        style.configure('TLabelFrame', background=bg_color, foreground=fg_color)
        style.configure('TLabelFrame.Label', background=bg_color, foreground=fg_color)
        style.configure('TEntry', fieldbackground='#404040', foreground=fg_color,
                       bordercolor='#555555', lightcolor='#555555', darkcolor='#555555')
        style.configure('TButton', background='#404040', foreground=fg_color,
                       bordercolor='#555555', lightcolor='#555555', darkcolor='#555555')
        style.map('TButton', background=[('active', '#505050')])

        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="🚀 GPT Account Generator",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # Create button
        self.create_btn = ttk.Button(main_frame, text="Create New Account",
                                    command=self.create_account_thread,
                                    style='Create.TButton')
        self.create_btn.pack(pady=(0, 15))

        # Progress frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X)

        # Status label
        self.status_label = ttk.Label(main_frame, text="Click 'Create New Account' to start",
                                     font=('Arial', 10), foreground='gray')
        self.status_label.pack(pady=(5, 20))

        # Credentials frame
        cred_frame = ttk.LabelFrame(main_frame, text="GPT Credentials",
                                   style='Heading.TLabel', padding=20)
        cred_frame.pack(fill=tk.X, pady=(0, 15))

        # Email row
        email_frame = ttk.Frame(cred_frame)
        email_frame.pack(fill=tk.X, pady=5)

        ttk.Label(email_frame, text="📧 GPT Email:", font=('Arial', 11, 'bold')).pack(side=tk.LEFT)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(email_frame, textvariable=self.email_var,
                                    font=('Consolas', 10), state='readonly', width=40)
        self.email_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        self.email_copy_btn = ttk.Button(email_frame, text="Copy", width=8,
                                        command=lambda: self.copy_to_clipboard(self.email_var.get(), "Email"))
        self.email_copy_btn.pack(side=tk.RIGHT)

        # Password row
        password_frame = ttk.Frame(cred_frame)
        password_frame.pack(fill=tk.X, pady=5)

        ttk.Label(password_frame, text="🔑 GPT Password:", font=('Arial', 11, 'bold')).pack(side=tk.LEFT)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(password_frame, textvariable=self.password_var,
                                       font=('Consolas', 10), state='readonly', width=40)
        self.password_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        self.password_copy_btn = ttk.Button(password_frame, text="Copy", width=8,
                                           command=lambda: self.copy_to_clipboard(self.password_var.get(), "Password"))
        self.password_copy_btn.pack(side=tk.RIGHT)

        # 2FA Link row
        link_frame = ttk.Frame(cred_frame)
        link_frame.pack(fill=tk.X, pady=5)

        ttk.Label(link_frame, text="🔐 2FA Code Link:", font=('Arial', 11, 'bold')).pack(side=tk.LEFT)
        self.link_var = tk.StringVar()
        self.link_entry = ttk.Entry(link_frame, textvariable=self.link_var,
                                   font=('Consolas', 10), state='readonly', width=40)
        self.link_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        self.link_copy_btn = ttk.Button(link_frame, text="Copy", width=8,
                                       command=lambda: self.copy_to_clipboard(self.link_var.get(), "2FA Link"))
        self.link_copy_btn.pack(side=tk.RIGHT)

        # Initially disable copy buttons
        self.disable_copy_buttons()

    def copy_to_clipboard(self, text, field_name):
        """Copy text to clipboard"""
        if not text or text == "":
            messagebox.showwarning("Nothing to Copy", f"No {field_name.lower()} to copy!")
            return

        try:
            # Try pyperclip first
            import pyperclip
            pyperclip.copy(text)
            messagebox.showinfo("Copied!", f"{field_name} copied to clipboard!")
        except ImportError:
            # Fallback to tkinter clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.root.update()
            messagebox.showinfo("Copied!", f"{field_name} copied to clipboard!")
        except Exception as e:
            messagebox.showerror("Copy Failed", f"Failed to copy {field_name.lower()}: {str(e)}")

    def generate_account_data(self):
        """Generate dummy account data"""
        first_name = self.fake.first_name()
        last_name = self.fake.last_name()

        return {
            'name': f"{first_name} {last_name}",
            'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com",
            'phone': f"{random.randint(**********, **********)}",
            'password': ''.join(random.choice(string.ascii_letters + string.digits + "!@#$%") for _ in range(10)),
            'country': 'France'
        }

    def decode_cf_email(self, encoded):
        """Decode CloudFlare email protection"""
        try:
            if not encoded:
                return None
            key = int(encoded[:2], 16)
            decoded = ''
            for i in range(2, len(encoded), 2):
                decoded += chr(int(encoded[i:i+2], 16) ^ key)
            return decoded
        except:
            return None

    def create_account(self):
        """Create account and extract credentials"""
        try:
            # Update status
            self.root.after(0, lambda: self.update_status("Generating account data..."))

            # Generate data
            account_data = self.generate_account_data()

            self.root.after(0, lambda: self.update_status("Creating account..."))

            # Submit registration
            response = self.session.post(self.base_url, data=account_data, allow_redirects=True)

            if response.status_code == 200 and "CG-AI" in response.text:
                self.root.after(0, lambda: self.update_status("✅ Account created! Extracting credentials..."))

                # Extract credentials
                credentials = self.extract_credentials(response)

                if credentials and credentials['email'] and credentials['password']:
                    # Update UI with credentials
                    self.root.after(0, lambda: self.update_credentials(credentials))
                    self.root.after(0, lambda: self.update_status("🎉 GPT account generated successfully!"))
                else:
                    self.root.after(0, lambda: self.update_status("❌ Could not extract GPT credentials"))
            else:
                self.root.after(0, lambda: self.update_status("❌ Account creation failed - please try again"))

        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"❌ Error: {str(e)}"))
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self.finish_creation)

    def extract_credentials(self, response):
        """Extract CG AI credentials from dashboard"""
        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # Find CG-AI section
            cg_ai_section = soup.find('details')
            if not cg_ai_section:
                return None

            summary = cg_ai_section.find('summary')
            if not summary or 'CG-AI' not in summary.get_text():
                return None

            # Extract email
            email_element = cg_ai_section.find('a', class_='__cf_email__')
            email = None
            if email_element:
                email_encoded = email_element.get('data-cfemail', '')
                email = self.decode_cf_email(email_encoded)

            # Extract password
            password = None
            cred_div = cg_ai_section.find('div', style=lambda x: x and 'monospace' in x)
            if cred_div:
                text_content = cred_div.get_text()
                password_match = re.search(r'Password\s*➜\s*([a-zA-Z0-9]+)', text_content)
                if password_match:
                    password = password_match.group(1)

            # Extract verification link
            verification_link = None
            verification_element = cg_ai_section.find('a', href=re.compile(r'cgcode'))
            if verification_element:
                verification_link = verification_element.get('href')
                if not verification_link.startswith('http'):
                    verification_link = urljoin(self.base_url, verification_link)

            return {
                'email': email,
                'password': password,
                'verification_link': verification_link
            }

        except Exception as e:
            return None

    def update_status(self, message):
        """Update status label"""
        self.status_label.config(text=message)

    def update_credentials(self, credentials):
        """Update UI with extracted credentials"""
        self.email_var.set(credentials['email'] or 'Not found')
        self.password_var.set(credentials['password'] or 'Not found')
        self.link_var.set(credentials['verification_link'] or 'Not found')

        # Enable copy buttons
        self.enable_copy_buttons()

    def enable_copy_buttons(self):
        """Enable all copy buttons"""
        self.email_copy_btn.configure(state='normal')
        self.password_copy_btn.configure(state='normal')
        self.link_copy_btn.configure(state='normal')

    def disable_copy_buttons(self):
        """Disable all copy buttons"""
        self.email_copy_btn.configure(state='disabled')
        self.password_copy_btn.configure(state='disabled')
        self.link_copy_btn.configure(state='disabled')

    def create_account_thread(self):
        """Start account creation in a separate thread"""
        # Disable button and start progress
        self.create_btn.configure(state='disabled', text="Creating...")
        self.progress.start()

        # Clear previous credentials
        self.email_var.set('')
        self.password_var.set('')
        self.link_var.set('')
        self.disable_copy_buttons()

        # Start creation in background thread
        thread = threading.Thread(target=self.create_account, daemon=True)
        thread.start()

    def finish_creation(self):
        """Re-enable UI after account creation"""
        self.create_btn.configure(state='normal', text="Create New Account")
        self.progress.stop()

def main():
    root = tk.Tk()
    app = OxaamGUI(root)

    # Handle window closing
    def on_closing():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
